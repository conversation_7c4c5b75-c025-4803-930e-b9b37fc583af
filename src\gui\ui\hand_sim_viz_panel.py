# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'hand_sim_viz_panel.ui'
##
## Created by: Qt User Interface Compiler version 6.4.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QHBoxLayout, QLabel, QPushButton,
    QSizePolicy, QSpacerItem, QTabWidget, QVBoxLayout,
    QWidget)

class Ui_Form(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"Form")
        Form.resize(600, 669)
        sizePolicy = QSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(Form.sizePolicy().hasHeightForWidth())
        Form.setSizePolicy(sizePolicy)
        self.verticalLayout_8 = QVBoxLayout(Form)
        self.verticalLayout_8.setObjectName(u"verticalLayout_8")
        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setSpacing(0)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.verticalLayout_6 = QVBoxLayout()
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.horizontalLayout_head_left = QHBoxLayout()
        self.horizontalLayout_head_left.setObjectName(u"horizontalLayout_head_left")
        self.label_sh_left = QLabel(Form)
        self.label_sh_left.setObjectName(u"label_sh_left")

        self.horizontalLayout_head_left.addWidget(self.label_sh_left)

        self.label_name_left = QLabel(Form)
        self.label_name_left.setObjectName(u"label_name_left")
        sizePolicy1 = QSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.label_name_left.sizePolicy().hasHeightForWidth())
        self.label_name_left.setSizePolicy(sizePolicy1)

        self.horizontalLayout_head_left.addWidget(self.label_name_left)

        self.horizontalSpacer = QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.horizontalLayout_head_left.addItem(self.horizontalSpacer)


        self.verticalLayout_6.addLayout(self.horizontalLayout_head_left)

        self.tabWidget_model_left = QTabWidget(Form)
        self.tabWidget_model_left.setObjectName(u"tabWidget_model_left")
        self.tab = QWidget()
        self.tab.setObjectName(u"tab")
        self.verticalLayout_2 = QVBoxLayout(self.tab)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_urdf_left = QVBoxLayout()
        self.verticalLayout_urdf_left.setSpacing(0)
        self.verticalLayout_urdf_left.setObjectName(u"verticalLayout_urdf_left")

        self.verticalLayout_2.addLayout(self.verticalLayout_urdf_left)

        self.tabWidget_model_left.addTab(self.tab, "")
        self.tab_2 = QWidget()
        self.tab_2.setObjectName(u"tab_2")
        self.verticalLayout_3 = QVBoxLayout(self.tab_2)
        self.verticalLayout_3.setSpacing(0)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.verticalLayout_3.addItem(self.verticalSpacer)

        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setSpacing(0)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_4)

        self.pushButton_connect_left = QPushButton(self.tab_2)
        self.pushButton_connect_left.setObjectName(u"pushButton_connect_left")
        sizePolicy.setHeightForWidth(self.pushButton_connect_left.sizePolicy().hasHeightForWidth())
        self.pushButton_connect_left.setSizePolicy(sizePolicy)
        self.pushButton_connect_left.setMinimumSize(QSize(200, 58))
        self.pushButton_connect_left.setMaximumSize(QSize(200, 58))

        self.horizontalLayout_2.addWidget(self.pushButton_connect_left)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_5)


        self.verticalLayout_3.addLayout(self.horizontalLayout_2)

        self.verticalSpacer_2 = QSpacerItem(20, 120, QSizePolicy.Minimum, QSizePolicy.Fixed)

        self.verticalLayout_3.addItem(self.verticalSpacer_2)

        self.tabWidget_model_left.addTab(self.tab_2, "")

        self.verticalLayout_6.addWidget(self.tabWidget_model_left)

        self.verticalLayout_6.setStretch(1, 1)

        self.horizontalLayout_4.addLayout(self.verticalLayout_6)

        self.verticalLayout_7 = QVBoxLayout()
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.horizontalLayout_head_right = QHBoxLayout()
        self.horizontalLayout_head_right.setObjectName(u"horizontalLayout_head_right")
        self.label_sh_right = QLabel(Form)
        self.label_sh_right.setObjectName(u"label_sh_right")

        self.horizontalLayout_head_right.addWidget(self.label_sh_right)

        self.label_name_right = QLabel(Form)
        self.label_name_right.setObjectName(u"label_name_right")
        sizePolicy1.setHeightForWidth(self.label_name_right.sizePolicy().hasHeightForWidth())
        self.label_name_right.setSizePolicy(sizePolicy1)

        self.horizontalLayout_head_right.addWidget(self.label_name_right)

        self.horizontalSpacer_8 = QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.horizontalLayout_head_right.addItem(self.horizontalSpacer_8)


        self.verticalLayout_7.addLayout(self.horizontalLayout_head_right)

        self.tabWidget_model_right = QTabWidget(Form)
        self.tabWidget_model_right.setObjectName(u"tabWidget_model_right")
        self.tab_3 = QWidget()
        self.tab_3.setObjectName(u"tab_3")
        self.verticalLayout_4 = QVBoxLayout(self.tab_3)
        self.verticalLayout_4.setSpacing(0)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.verticalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_urdf_right = QVBoxLayout()
        self.verticalLayout_urdf_right.setSpacing(0)
        self.verticalLayout_urdf_right.setObjectName(u"verticalLayout_urdf_right")

        self.verticalLayout_4.addLayout(self.verticalLayout_urdf_right)

        self.tabWidget_model_right.addTab(self.tab_3, "")
        self.tab_4 = QWidget()
        self.tab_4.setObjectName(u"tab_4")
        self.verticalLayout_5 = QVBoxLayout(self.tab_4)
        self.verticalLayout_5.setSpacing(0)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.verticalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.verticalSpacer_3 = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.verticalLayout_5.addItem(self.verticalSpacer_3)

        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setSpacing(0)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalSpacer_6 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_6)

        self.pushButton_connect_right = QPushButton(self.tab_4)
        self.pushButton_connect_right.setObjectName(u"pushButton_connect_right")
        sizePolicy.setHeightForWidth(self.pushButton_connect_right.sizePolicy().hasHeightForWidth())
        self.pushButton_connect_right.setSizePolicy(sizePolicy)
        self.pushButton_connect_right.setMinimumSize(QSize(200, 58))
        self.pushButton_connect_right.setMaximumSize(QSize(200, 58))

        self.horizontalLayout_3.addWidget(self.pushButton_connect_right)

        self.horizontalSpacer_7 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_7)


        self.verticalLayout_5.addLayout(self.horizontalLayout_3)

        self.verticalSpacer_4 = QSpacerItem(20, 120, QSizePolicy.Minimum, QSizePolicy.Fixed)

        self.verticalLayout_5.addItem(self.verticalSpacer_4)

        self.tabWidget_model_right.addTab(self.tab_4, "")

        self.verticalLayout_7.addWidget(self.tabWidget_model_right)

        self.verticalLayout_7.setStretch(1, 1)

        self.horizontalLayout_4.addLayout(self.verticalLayout_7)


        self.verticalLayout_8.addLayout(self.horizontalLayout_4)

        self.verticalLayout = QVBoxLayout()
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.label_tip = QLabel(Form)
        self.label_tip.setObjectName(u"label_tip")
        self.label_tip.setAlignment(Qt.AlignCenter)

        self.verticalLayout.addWidget(self.label_tip)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.horizontalLayout.addItem(self.horizontalSpacer_2)

        self.verticalLayout_switch = QVBoxLayout()
        self.verticalLayout_switch.setSpacing(0)
        self.verticalLayout_switch.setObjectName(u"verticalLayout_switch")

        self.horizontalLayout.addLayout(self.verticalLayout_switch)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.horizontalLayout.addItem(self.horizontalSpacer_3)


        self.verticalLayout.addLayout(self.horizontalLayout)


        self.verticalLayout_8.addLayout(self.verticalLayout)

        self.verticalLayout_8.setStretch(0, 1)

        self.retranslateUi(Form)

        self.tabWidget_model_left.setCurrentIndex(0)
        self.tabWidget_model_right.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(Form)
    # setupUi

    def retranslateUi(self, Form):
        Form.setWindowTitle(QCoreApplication.translate("Form", u"Form", None))
        self.label_sh_left.setText(QCoreApplication.translate("Form", u"x", None))
        self.label_name_left.setText("")
        self.tabWidget_model_left.setTabText(self.tabWidget_model_left.indexOf(self.tab), QCoreApplication.translate("Form", u"urdf", None))
        self.pushButton_connect_left.setText("")
        self.tabWidget_model_left.setTabText(self.tabWidget_model_left.indexOf(self.tab_2), QCoreApplication.translate("Form", u"connect_button", None))
        self.label_sh_right.setText(QCoreApplication.translate("Form", u"x", None))
        self.label_name_right.setText("")
        self.tabWidget_model_right.setTabText(self.tabWidget_model_right.indexOf(self.tab_3), QCoreApplication.translate("Form", u"urdf", None))
        self.pushButton_connect_right.setText("")
        self.tabWidget_model_right.setTabText(self.tabWidget_model_right.indexOf(self.tab_4), QCoreApplication.translate("Form", u"connect_button", None))
        self.label_tip.setText(QCoreApplication.translate("Form", u"Hand Control", None))
    # retranslateUi

